<view class="container">
  <block wx:if="{{!loading && serviceDetail}}">
    <!-- 支付头部 -->
    <view class="pay-header">
      <view class="pay-icon">💰</view>
      <text class="pay-title">确认支付</text>
      <text class="pay-desc">请确认以下追加服务信息</text>
    </view>

    <!-- 服务信息 -->
    <view class="service-section">
      <view class="section-title">服务信息</view>
      <view class="service-list">
        <view wx:for="{{serviceDetail.details}}" wx:key="id" class="service-item">
          <view class="service-info">
            <text class="service-name">{{item.serviceName}}</text>
            <text class="service-spec">数量：{{item.quantity}} | 单价：¥{{item.servicePrice}}</text>
          </view>
          <text class="service-total">¥{{item.servicePrice * item.quantity}}</text>
        </view>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="price-section">
      <view class="section-title">价格明细</view>
      <view class="price-details">
        <view class="price-row">
          <text class="price-label">服务原价</text>
          <text class="price-value">¥{{serviceDetail.originalPrice}}</text>
        </view>
        <view wx:if="{{serviceDetail.cardDeduction > 0}}" class="price-row discount">
          <text class="price-label">权益卡优惠</text>
          <text class="price-value">-¥{{serviceDetail.cardDeduction}}</text>
        </view>
        <view wx:if="{{serviceDetail.couponDeduction > 0}}" class="price-row discount">
          <text class="price-label">优惠券优惠</text>
          <text class="price-value">-¥{{serviceDetail.couponDeduction}}</text>
        </view>
      </view>
    </view>

    <!-- 支付金额 -->
    <view class="pay-amount-section">
      <view class="amount-label">应付金额</view>
      <view class="amount-value">¥{{serviceDetail.totalFee}}</view>
    </view>

    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="order-info">
        <view class="info-row">
          <text class="info-label">订单号</text>
          <text class="info-value">{{serviceDetail.sn}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">申请时间</text>
          <text class="info-value">{{serviceDetail.createdAt}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">确认时间</text>
          <text class="info-value">{{serviceDetail.confirmTime}}</text>
        </view>
      </view>
    </view>

    <!-- 客户信息 -->
    <view wx:if="{{serviceDetail.orderDetail && serviceDetail.orderDetail.order}}" class="customer-section">
      <view class="section-title">客户信息</view>
      <view class="customer-info">
        <view class="info-row">
          <text class="info-label">客户姓名</text>
          <text class="info-value">{{serviceDetail.orderDetail.order.customer.name}}</text>
        </view>
        <view class="info-row">
          <text class="info-label">联系电话</text>
          <text class="info-value">{{serviceDetail.orderDetail.order.customer.phone}}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-method-section">
      <view class="section-title">支付方式</view>
      <view class="payment-method">
        <view class="method-item selected">
          <view class="method-icon">💳</view>
          <view class="method-info">
            <text class="method-name">微信支付</text>
            <text class="method-desc">安全便捷的支付方式</text>
          </view>
          <view class="method-check">✓</view>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="pay-button-section">
      <button class="pay-btn" bindtap="confirmPay" disabled="{{paying}}">
        {{paying ? '支付中...' : '确认支付 ¥' + serviceDetail.totalFee}}
      </button>
      <button class="detail-btn" bindtap="viewServiceDetail">查看详情</button>
    </view>

    <!-- 支付说明 -->
    <view class="pay-notice">
      <text class="notice-title">支付说明</text>
      <text class="notice-text">• 支付成功后，服务人员将为您提供追加服务</text>
      <text class="notice-text">• 如有疑问，请联系客服：400-123-4567</text>
      <text class="notice-text">• 支付过程中请勿关闭页面</text>
    </view>
  </block>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>



  <!-- 自定义模态框 -->
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    bind:confirm="handleModalConfirm"
    bind:cancel="handleModalCancel"
    bind:handlePayConfirm="handlePayConfirm"
    bind:handleModalCancel="handleModalCancel"
    bind:handlePaymentSuccess="handlePaymentSuccess"
  ></custom-modal>
</view>
