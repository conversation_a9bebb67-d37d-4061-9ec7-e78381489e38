/* 页面容器 */
.container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding: 20rpx 20rpx 120rpx;
}

/* 状态卡片样式 */
.status-card {
  background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid #ff4391;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  background: radial-gradient(circle, rgba(255, 67, 145, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(30rpx, -30rpx);
}

.status-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  z-index: 1;
  position: relative;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 1;
  position: relative;
}

.status-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 信息区块样式 */
.info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.02);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;
}

.section-title::before {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #ff4391 0%, #ff7ba7 100%);
  border-radius: 1rpx;
}

/* 信息列表样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.info-value.link {
  color: #2f83ff;
  text-decoration: underline;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #2f83ff;
  box-shadow: 0 2rpx 12rpx rgba(47, 131, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.service-item::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle, rgba(47, 131, 255, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(20rpx, -20rpx);
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 1;
  position: relative;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.service-spec {
  font-size: 24rpx;
  color: #666;
  background-color: rgba(47, 131, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  width: fit-content;
}

.service-total {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4391;
  margin-left: 20rpx;
  z-index: 1;
  position: relative;
}

/* 价格明细样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  background-color: #fafbfc;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  position: relative;
}

.price-row.discount {
  color: #ff4391;
  background-color: rgba(255, 67, 145, 0.05);
  margin: 0 -20rpx;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}

.price-row.discount::before {
  content: "💰";
  position: absolute;
  left: 20rpx;
  font-size: 20rpx;
  opacity: 0.6;
}

.price-row.discount .price-label {
  margin-left: 30rpx;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e0e0e0;
  font-weight: bold;
  background: linear-gradient(135deg, rgba(255, 67, 145, 0.1) 0%, rgba(255, 123, 167, 0.05) 100%);
  margin: 16rpx -20rpx 0;
  padding: 20rpx;
  border-radius: 8rpx;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price-value.total-price {
  font-size: 32rpx;
  color: #ff4391;
  font-weight: bold;
}

/* 优惠信息样式 */
.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  background: linear-gradient(135deg, #fff6f9 0%, #ffffff 100%);
  border: 2rpx solid #ffdbea;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 79, 143, 0.08);
  position: relative;
}

.discount-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(180deg, #ff4391 0%, #ff7ba7 100%);
  border-radius: 16rpx 0 0 16rpx;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-left: 10rpx;
}

.discount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discount-type {
  font-size: 28rpx;
  color: #ff4391;
  font-weight: bold;
}

.discount-amount {
  font-size: 28rpx;
  color: #ff4391;
  font-weight: bold;
}

.discount-detail {
  margin-top: 5rpx;
}

.discount-rate {
  font-size: 24rpx;
  color: #666;
}

.discount-name {
  margin-top: 5rpx;
}

.name-text {
  font-size: 24rpx;
  color: #999;
}

/* 客户信息样式 */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.customer-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.customer-info .info-item:last-child {
  border-bottom: none;
}

.customer-info .info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  width: 160rpx;
}

.customer-info .info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

/* 备注信息样式 */
.remark-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ff4391;
  position: relative;
}

.remark-content::before {
  content: "💬";
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  font-size: 24rpx;
  opacity: 0.6;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-left: 40rpx;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 操作按钮样式 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, #ffffff 100%);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(229, 229, 229, 0.5);
  display: flex;
  gap: 20rpx;
  z-index: 100;
  box-shadow: 0 -4rpx 30rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:active::before {
  opacity: 1;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff4391 0%, #ff7ba7 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(255, 67, 145, 0.4);
}

.action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 67, 145, 0.5);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 1rpx solid rgba(224, 224, 224, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.action-btn.secondary:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(2rpx);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  min-height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
  position: relative;
}

.loading-text::before {
  content: "";
  position: absolute;
  left: -40rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #ff4391;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}
